// 二级操作类型定义
export interface SecondaryAction {
  id: string;
  label: string;
  action: string;
  icon?: string;
}

// 菜单项类型定义
export interface MenuItemType {
  id: string;
  label: string;
  action: string;
  title?: string;
  icon?: string;
  type: 'button' | 'icon' | 'close' | 'divider';
  position: 'main' | 'dropdown';
  secondaryActions?: readonly SecondaryAction[];
}

// 二级操作选项定义
export const secondaryActionTypes = {
  CONTINUE_ASKING: {
    id: 'continue-asking',
    label: '继续问',
    action: 'continue-asking',
    icon: 'chat'
  },
  ADJUST: {
    id: 'adjust',
    label: '调整',
    action: 'adjust',
    icon: 'edit'
  },
  DEPRECATE: {
    id: 'deprecate',
    label: '弃用',
    action: 'deprecate',
    icon: 'disable'
  },
  INSERT_BELOW: {
    id: 'insert-below',
    label: '插入到下方',
    action: 'insert-below',
    icon: 'insert'
  },
  REPLACE_ORIGINAL: {
    id: 'replace-original',
    label: '替换原文',
    action: 'replace-original',
    icon: 'replace'
  }
} as const;

// 不同功能支持的二级操作组合
export const secondaryActionGroups = {
  // 翻译功能支持的二级操作
  translate: [
    secondaryActionTypes.CONTINUE_ASKING,
    secondaryActionTypes.INSERT_BELOW,
    secondaryActionTypes.REPLACE_ORIGINAL
  ],
  // 总结功能支持的二级操作
  summary: [
    secondaryActionTypes.CONTINUE_ASKING,
    secondaryActionTypes.INSERT_BELOW,
    secondaryActionTypes.REPLACE_ORIGINAL
  ],
  // 润色相关功能支持全部二级操作
  'text-polisher': [
    secondaryActionTypes.CONTINUE_ASKING,
    // secondaryActionTypes.ADJUST,
    // secondaryActionTypes.DEPRECATE,
    secondaryActionTypes.INSERT_BELOW,
    secondaryActionTypes.REPLACE_ORIGINAL
  ],
  // 其他功能的默认二级操作
  default: [
    secondaryActionTypes.CONTINUE_ASKING,
    // secondaryActionTypes.ADJUST,
    // secondaryActionTypes.DEPRECATE,
    secondaryActionTypes.INSERT_BELOW,
    secondaryActionTypes.REPLACE_ORIGINAL
  ]
} as const;

// 菜单项配置
export const menuItems: MenuItemType[] = [
  // 主要按钮区域
  {
    id: 'ai-assistant',
    label: '',
    action: 'open-panel',
    title: 'AI助手面板',
    type: 'icon',
    position: 'main',
    icon: 'chatLogo'
  },
  {
    id: 'summary',
    label: '总结',
    action: 'summary',
    type: 'button',
    position: 'main',
    secondaryActions: secondaryActionGroups.summary
  },
  {
    id: 'translate',
    label: '翻译',
    action: 'translate',
    type: 'button',
    position: 'main',
    secondaryActions: secondaryActionGroups.translate
  },

  // 下拉菜单项
  {
    id: 'text-condenser',
    label: '缩写',
    action: 'text-condenser',
    type: 'button',
    position: 'dropdown',
    secondaryActions: secondaryActionGroups.default
  },
  {
    id: 'text-expander',
    label: '扩写',
    action: 'text-expander',
    type: 'button',
    position: 'dropdown',
    secondaryActions: secondaryActionGroups.default
  },
  {
    id: 'text-polisher',
    label: '润色',
    action: 'text-polisher',
    type: 'button',
    position: 'dropdown',
    secondaryActions: secondaryActionGroups['text-polisher']
  },
  {
    id: 'grammar-corrector',
    label: '修正',
    action: 'grammar-corrector',
    type: 'button',
    position: 'dropdown',
    secondaryActions: secondaryActionGroups.default
  }
];

// AI处理类操作列表
export const aiActions = ['summary', 'translate', 'text-condenser', 'text-expander', 'text-polisher', 'grammar-corrector'];

// 获取主要按钮（显示在工具栏上的按钮）
export const getMainButtons = (): MenuItemType[] => {
  return menuItems.filter(item => item.position === 'main');
};

// 获取下拉菜单项
export const getDropdownItems = (): MenuItemType[] => {
  return menuItems.filter(item => item.position === 'dropdown');
};

// 根据action判断是否为AI处理类操作
export const isAIAction = (action: string): boolean => {
  return aiActions.includes(action);
};

// 获取菜单项的二级操作
export const getSecondaryActions = (menuItemId: string): readonly SecondaryAction[] => {
  const menuItem = menuItems.find(item => item.id === menuItemId);
  return menuItem?.secondaryActions || [];
};

// 根据菜单项ID和二级操作ID获取完整的操作信息
export const getSecondaryAction = (menuItemId: string, secondaryActionId: string): SecondaryAction | undefined => {
  const secondaryActions = getSecondaryActions(menuItemId);
  return secondaryActions.find(action => action.id === secondaryActionId);
};

// 判断菜单项是否支持特定的二级操作
export const hasSecondaryAction = (menuItemId: string, secondaryActionId: string): boolean => {
  const secondaryActions = getSecondaryActions(menuItemId);
  return secondaryActions.some(action => action.id === secondaryActionId);
};

// 获取所有二级操作类型
export const getAllSecondaryActionTypes = (): SecondaryAction[] => {
  return Object.values(secondaryActionTypes);
};
