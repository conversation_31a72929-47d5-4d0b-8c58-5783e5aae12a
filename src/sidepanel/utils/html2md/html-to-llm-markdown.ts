import { domToLLMMarkdown } from './llm-markdown';

function removeComments(doc: Document) {
    const treeWalker = doc.createTreeWalker(doc, NodeFilter.SHOW_COMMENT);
    const toRemove: Comment[] = [];
    let node = treeWalker.nextNode();
    while (node) {
        toRemove.push(node as Comment);
        node = treeWalker.nextNode();
    }
    toRemove.forEach((c) => c.parentNode?.removeChild(c));
}

function cleanDomDocument(doc: Document, discardSelectors: string[]) {
    // Drop noisy/irrelevant nodes
    for (const sel of discardSelectors) {
        doc.querySelectorAll(sel).forEach((x) => x.remove());
    }

    // Normalize images: drop heavy data URLs and srcset
    doc.querySelectorAll('img[src],img[data-src]').forEach((x) => {
        const src = x.getAttribute('src') || x.getAttribute('data-src') || '';
        if (src.startsWith('data:')) {
            x.setAttribute('src', 'blob:opaque');
        }
        x.removeAttribute('data-src');
        x.removeAttribute('srcset');
    });

    // Minify style/class/attrs footprint; keep display:none signals
    doc.querySelectorAll('[style]').forEach((x) => {
        const style = (x.getAttribute('style') || '').toLowerCase();
        if (style.startsWith('display: none')) {
            return;
        }
        x.removeAttribute('style');
    });

    doc.querySelectorAll('*').forEach((x) => {
        const attrs = Array.from(x.getAttributeNames());
        for (const attr of attrs) {
            if (attr.startsWith('data-') || attr.startsWith('aria-')) {
                x.removeAttribute(attr);
            }
        }
    });

    // 清理可能导致 undefined 的元素
    doc.querySelectorAll('*').forEach((x) => {
        // 检查是否有空的文本节点或 undefined 内容
        if (x.childNodes) {
            for (let i = x.childNodes.length - 1; i >= 0; i--) {
                const child = x.childNodes[i];
                if (child.nodeType === Node.TEXT_NODE) {
                    const text = child.textContent || '';
                    if (text.trim() === '' || text.includes('undefined')) {
                        child.remove();
                    }
                }
            }
        }
    });

    removeComments(doc);
}

function normalizeBlankLines(text: string) {
    if (!text || typeof text !== 'string') {
        return '';
    }

    return text
        .split(/\r?\n/g)
        .filter((line, idx, arr) => {
            const trimmed = line.trim();
            // 过滤掉包含 undefined 的行
            if (trimmed.includes('undefined')) {
                return false;
            }
            return trimmed || (idx > 0 && arr[idx - 1].trim());
        })
        .join('\n');
}

export function htmlToLLMMarkdown(html: string, options?: any): string {
    const discardSelectors = options?.discardSelectors || [
        'script',
        'link',
        'style',
        'textarea',
        'select>option',
        'svg',
        'iframe',
        'canvas',
        'meta',
        'noscript',
        'embed',
        'nav', // 添加导航元素到过滤列表
        'header', // 添加头部元素到过滤列表
        'footer', // 添加底部元素到过滤列表
    ];

    let cleanedHtml = html || '';

    // 在浏览器扩展环境中，我们总是有 DOMParser 可用
    const parser = new DOMParser();
    const doc = parser.parseFromString(cleanedHtml, 'text/html');
    if (!doc.documentElement) {
        const wrapped = parser.parseFromString(`<html><body>${cleanedHtml}</body></html>`, 'text/html');
        cleanDomDocument(wrapped, discardSelectors);
        cleanedHtml = normalizeBlankLines('<!DOCTYPE html>\n' + wrapped.documentElement.outerHTML);
    } else {
        cleanDomDocument(doc, discardSelectors);
        cleanedHtml = normalizeBlankLines('<!DOCTYPE html>\n' + doc.documentElement.outerHTML);
    }

    // 额外的清理：移除任何剩余的 undefined 字符串
    cleanedHtml = cleanedHtml.replace(/undefined\s*/g, '');

    // Reuse the Turndown rules from domToLLMMarkdown
    const result = domToLLMMarkdown(cleanedHtml, options).markdown;

    // 最终清理：移除结果中的 undefined 字符串
    return result.replace(/undefined\s*/g, '').trim();
}
