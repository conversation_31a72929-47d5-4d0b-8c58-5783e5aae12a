import { getCurrentTab } from '@src/sidepanel/utils'
import { MessageType } from '@src/common/const'
import { convertHtmlToLlmMarkdown } from '@src/sidepanel/utils/html2md'

// 翻译页面函数
export const handleTranslateCurrentPage = async (messageApi?: any) => {
  try {
    const currentTab = await getCurrentTab(messageApi)
    console.log('开始翻译页面...', currentTab);
    if (!currentTab) {
      return
    }
    chrome.tabs.sendMessage(currentTab.id, {
      type: MessageType.START_TRANSLATE,
    })
  } catch (error) {
    console.error('翻译页面失败:', error);
  }
}



// 总结页面函数
export const handleSummaryCurrentPage = async (messageApi?: any, chatUiRef?: any) => {
  const currentTab = await getCurrentTab(messageApi)

  async function getPage(): Promise<string> {
    const currentTab = await getCurrentTab(messageApi);
    return new Promise((resolve) => {
      chrome.scripting.executeScript({
        target: { tabId: currentTab.id },
        func: () => '<!DOCTYPE html>\n' + document.documentElement.outerHTML
      }).then((r) => resolve(r?.[0]?.result || ''));
    });
  }

  // // 将 HTML 字符串转换为纯文本（不包含任何标签）
  // const htmlToPlainText = (html: string): string => {
  //   try {
  //     const parser = new DOMParser()
  //     const doc = parser.parseFromString(html || '', 'text/html')
  //     doc.querySelectorAll('script, style, noscript, canvas, svg, iframe, meta, link, embed').forEach((el) => el.remove())
  //     const rawText = (doc.body?.textContent ?? doc.documentElement?.textContent ?? '')
  //     return rawText
  //       .replace(/\u00a0/g, ' ')
  //       .replace(/[\t\r\f]+/g, ' ')
  //       .replace(/\s+/g, ' ')
  //       .trim()
  //   } catch (e) {
  //     // 回退方案：简单移除标签
  //     return (html || '')
  //       .replace(/<[^>]*>/g, ' ')
  //       .replace(/\u00a0/g, ' ')
  //       .replace(/\s+/g, ' ')
  //       .trim()
  //   }
  // }

  // const htmlContent = await extractPageContent(currentTab)
  // const plainText = htmlToPlainText(htmlContent)

  const html_content = await getPage()

  const markdownContent = convertHtmlToLlmMarkdown(html_content)

  if (chatUiRef?.current) {
    chatUiRef.current.chatContext.onSend('text', `网页内容：${markdownContent}`, {
      agentId: 'summary',
    }, [{
      type: 'citeweb',
      url: currentTab.url,
    }])
  }
}

// 提取页面内容的函数
export const extractPageContent = async (currentTab: any): Promise<string> => {
  return new Promise((resolve) => {
    chrome.scripting
      .executeScript({
        target: { tabId: currentTab.id },
        func: () => {
          // 非递归、深度优先遍历获取所有的dom元素
          const DFSDomTraversal = (root: any) => {
            if (!root) return

            const arr = []
            const queue = [root]
            let node = queue.shift()

            while (node) {
              arr.push(node)
              const childLen = node.children.length
              if (childLen) {
                for (let i = childLen - 1; i >= 0; i--) {
                  queue.unshift(node.children[i])
                }
              }
              node = queue.shift()
            }
            return arr
          }

          /**
           * 获取dom结构
           */
          function copyRootDomContent(root: any, copyRoot: any) {
            const rootDom = DFSDomTraversal(root)
            const copyRootDom = DFSDomTraversal(copyRoot)
            const removeElement = (originElement: any, eleIndex: any) => {
              const copyElement = copyRootDom[eleIndex]

              // 要过滤的标签列表
              const FILTER_TAGS = [
                'canvas',
                'svg',
                'img',
                'video',
                'audio',
                'iframe',
                'embed',
                'meta',
                'link',
                'script',
                'style',
                'hr',
              ]

              // 检查元素是否隐藏
              function isHidden(node: any) {
                if (!(node instanceof Element)) return false

                const style = window.getComputedStyle(node)
                return (
                  style.display === 'none' ||
                  style.visibility === 'hidden' ||
                  style.opacity === '0' ||
                  node.hasAttribute('hidden')
                )
              }

              if (
                FILTER_TAGS.includes(originElement.tagName?.toLowerCase())
              ) {
                copyElement.remove()
              } else if (isHidden(originElement)) {
                copyElement.remove()
              }
            }

            rootDom.forEach(removeElement)
            return new XMLSerializer().serializeToString(copyRoot)
          }

          const copyHtmlContent = () => {
            console.log('--------------copyHtmlContent-------------------')
            console.log(document.title)
            const root = document.documentElement
            const cloneRoot = root.cloneNode(true)
            const content = copyRootDomContent(root, cloneRoot)

            return content.replace(/\s+/g, ' ')
          }
          return copyHtmlContent()
        },
      })
      .then((result) => {
        resolve(result[0].result)
      })
  })
}