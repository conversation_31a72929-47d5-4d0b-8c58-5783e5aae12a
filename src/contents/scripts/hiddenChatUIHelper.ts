/**
 * HiddenChatUI 访问辅助工具
 * 提供访问和调用 HiddenChatUI 的封装函数
 */

// 声明全局类型（与 webAssistantManager.ts 保持一致）
declare global {
  interface Window {
    webAssistantManagerInitialized?: boolean
    webAssistantManager?: any  // 使用 any 避免循环依赖
  }
}

/**
 * 获取 HiddenChatUI 的 ref 实例
 * @returns HiddenChatUI ref 实例或 null
 */
export function getHiddenChatUIRef() {
  const manager = window.webAssistantManager
  if (!manager) {
    console.warn('hiddenChatUIHelper: WebAssistantManager not found on window')
    return null
  }

  const ref = manager.getHiddenChatUIRef()
  if (!ref) {
    console.warn('hiddenChatUIHelper: HiddenChatUI ref not available')
    return null
  }

  return ref
}

/**
 * 调用 HiddenChatUI 的 onSend 方法
 * @param type 消息类型
 * @param content 消息内容
 * @param options 选项参数
 * @param attachments 附件
 * @returns Promise<any>
 */
export async function callHiddenChatUIOnsend(type: string, content: string, options?: any, attachments?: any[]): Promise<any> {
  const manager = window.webAssistantManager
  if (!manager) {
    throw new Error('hiddenChatUIHelper: WebAssistantManager not initialized')
  }

  try {
    const result = await manager.callHiddenChatUIOnsend(type, content, options, attachments)
    return result
  } catch (error) {
    console.error('hiddenChatUIHelper: Failed to call HiddenChatUI:', error)
    throw error
  }
}

/**
 * 检查 HiddenChatUI 是否可用
 * @returns boolean
 */
export function isHiddenChatUIAvailable(): boolean {
  const manager = window.webAssistantManager
  return !!(manager && manager.getHiddenChatUIRef())
}

/**
 * 示例：使用 HiddenChatUI 进行翻译
 * 这个函数展示了如何调用 HiddenChatUI
 */
export async function exampleUseHiddenChatUI() {
  try {
    // 检查 HiddenChatUI 是否可用
    if (!isHiddenChatUIAvailable()) {
      console.warn('hiddenChatUIHelper: HiddenChatUI not available')
      return
    }

    // 获取 ref 实例（可选，用于直接访问）
    const chatUIRef = getHiddenChatUIRef()
    console.log('hiddenChatUIHelper: Got HiddenChatUI ref:', chatUIRef)

    // 调用翻译功能
    const result = await callHiddenChatUIOnsend(
      'text',
      '翻译这段文本：Hello World',
      { agentId: 'translate' }
    )

    console.log('hiddenChatUIHelper: Translation result:', result)
    return result
  } catch (error) {
    console.error('hiddenChatUIHelper: Example usage failed:', error)
  }
}

/**
 * 等待 WebAssistantManager 初始化完成
 * @param timeout 超时时间（毫秒）
 * @returns Promise<boolean>
 */
export function waitForWebAssistantManager(timeout: number = 5000): Promise<boolean> {
  return new Promise((resolve) => {
    const startTime = Date.now()

    const checkManager = () => {
      if (window.webAssistantManager && window.webAssistantManager.getHiddenChatUIRef()) {
        resolve(true)
        return
      }

      if (Date.now() - startTime > timeout) {
        console.warn('hiddenChatUIHelper: WebAssistantManager initialization timeout')
        resolve(false)
        return
      }

      setTimeout(checkManager, 100)
    }

    checkManager()
  })
}

/**
 * 安全调用 HiddenChatUI，会等待初始化完成
 * @param type 消息类型
 * @param content 消息内容
 * @param options 选项参数
 * @param attachments 附件
 * @returns Promise<any>
 */
export async function safeCallHiddenChatUI(type: string, content: string, options?: any, attachments?: any[]): Promise<any> {
  const isReady = await waitForWebAssistantManager()
  if (!isReady) {
    throw new Error('hiddenChatUIHelper: WebAssistantManager not ready within timeout')
  }

  return callHiddenChatUIOnsend(type, content, options, attachments)
}
